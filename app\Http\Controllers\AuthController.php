<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            new Middleware('guest', except: ['logout']),
        ];
    }

    public function login()
    {
        request()->validate([
            'email' => 'required',
            'password' => 'required'
        ]);

        $data = User::where('email', request()->email)->first();

        if (empty($data))
            return $this->failed('E-mail does not exists', 422);

        if (!Hash::check(request()->password, $data->password))
            return $this->failed('Password does not match', 422);

        $token = $data->createToken('ACCESS TOKEN', ['*'], Carbon::now()->addDay())->plainTextToken;

        return $this->success($token, 'Login Succeed');
    }

    public function logout()
    {
        auth()->guard('user')->user()?->currentAccessToken()?->delete();

        return $this->success(message: 'Logout Succeed');
    }
}
