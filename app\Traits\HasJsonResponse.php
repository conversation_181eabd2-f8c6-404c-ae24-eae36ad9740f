<?php

namespace App\Traits;

trait HasJsonResponse
{
    /**
     * A function that returns a success response in JSON format.
     *
     * @param mixed $data The data to be included in the response.
     * @param string $message The message to be included in the response.
     * @param int $code The HTTP status code to be used in the response.
     * @param string|null $statusCode The custom status code to be included in the response.
     * @param array $additionalData The custom data to be included in the response.
     * @return \Illuminate\Http\JsonResponse The JSON response with the filtered data.
     */
    public function success($data = null, $message = 'Success', $code = 200, $statusCode = null, $additionalData = [])
    {
        $response = [
            'status' => true,
            'message' => $message,
            'data' => $data
        ];

        if (!empty($statusCode)) {
            $response['status_code'] = $statusCode;
        }

        foreach ($additionalData as $key => $value) {
            $response[$key] = $value;
        }

        return response()->json(array_filter($response, function ($v, $k) {
            return !is_null($v);
        }, ARRAY_FILTER_USE_BOTH), $code);
    }

    /**
     * A function that handles a failed operation.
     *
     * @param string $message The message to be displayed for the failed operation. Default is 'Failed'.
     * @param int $code The HTTP response code for the failed operation. Default is 500.
     * @param string|null $statusCode The specific status code for the failed operation. Default is null.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the status and message.
     */
    public function failed($message = 'Failed', $code = 500, $statusCode = null)
    {
        $response = [
            'status' => false,
            'message' => $message,
        ];

        if (!empty($statusCode)) {
            $response['status_code'] = $statusCode;
        }

        return response()->json($response, $code);
    }

}
